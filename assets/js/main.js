document.addEventListener("DOMContentLoaded", () => {
    document.querySelector(".split-screen.yes")
        .addEventListener("wheel", (e) => {
            e.stopImmediatePropagation();
        }, { passive: false });
});

window.addEventListener("load", function() {
    const container = document.querySelector(".split-screen.yes");
    if (!container) return;

    function setMaxHeight() {
        let maxHeight = 0;

        container.childNodes.forEach(child => {
            if (child.nodeType === 1) { // tylko elementy
                const h = child.offsetHeight;
                if (h > maxHeight) {
                    maxHeight = h;
                }
            }
        });

        // ustawiamy wysoko<PERSON>ć z najwyższym priorytetem
        container.style.setProperty("height", maxHeight + "px", "important");
    }

    // pierwsze ustawienie po załadowaniu
    setMaxHeight();

    // aktualizacja przy zmianie rozmiaru okna
    window.addEventListener("resize", setMaxHeight);

    // obserwujemy zmiany w drzewie DOM i atrybutach
    const observer = new MutationObserver(() => {
        setMaxHeight();
    });

    observer.observe(container, {
        attributes: true,
        childList: true,
        subtree: true
    });
});

// Owl carousel
document.addEventListener("DOMContentLoaded", function () {
    const slider = document.querySelector(".category-slider-area");

    if (slider) {
        slider.addEventListener(
            "wheel",
            function (event) {
                // blokujemy domyślne i inne handlery
                event.stopImmediatePropagation();
            },
            { passive: false }
        );
    }
});

// Oferta
document.addEventListener('DOMContentLoaded', function () {
    const sliderLoops = document.querySelectorAll('.slider_loop, .slider_loop2, .slider_loop3, .slider_loop4');

    if (!sliderLoops.length) return;

    const isDesktop = jQuery(window).width() > 1024

    const waitForSwipers = setInterval(() => {
        let allReady = true;

        sliderLoops.forEach(loop => {
            const carousel = loop.querySelector('.elementor-image-carousel-wrapper');
            if (!carousel || !carousel.swiper) {
                allReady = false;
            }
        });

        if (!allReady) return;

        clearInterval(waitForSwipers);

        sliderLoops.forEach(loop => {
            const carousel = loop.querySelector('.elementor-image-carousel-wrapper');
            const swiper = carousel.swiper;

            // Stop autoplay initially (only for desktop)
            if (isDesktop) swiper.autoplay.stop();

            const prev = carousel.querySelector('.elementor-swiper-button-prev');
            const next = carousel.querySelector('.elementor-swiper-button-next');

            if (prev && next) {
                [prev, next].forEach(el => {
                    el.style.transition = 'opacity 0.3s ease';

                    if (isDesktop) {
                        el.style.opacity = '0';
                        el.style.pointerEvents = 'none';
                    } else {
                        el.style.opacity = '1';
                        el.style.pointerEvents = 'auto';
                    }
                });
            }

            if (isDesktop) {
                // Desktop behavior: autoplay on loop hover
                loop.addEventListener('mouseenter', () => {
                    swiper.autoplay.start();
                });

                loop.addEventListener('mouseleave', () => {
                    swiper.autoplay.stop();
                });

                // Pause autoplay and show arrows on carousel hover
                carousel.addEventListener('mouseenter', () => {
                    swiper.autoplay.stop();
                    showNavigation(carousel, true);
                });

                carousel.addEventListener('mouseleave', () => {
                    showNavigation(carousel, false);
                    if (loop.matches(':hover')) {
                        swiper.autoplay.start();
                    }
                });
            }
        });

        function showNavigation(wrapper, show) {
            const prev = wrapper.querySelector('.elementor-swiper-button-prev');
            const next = wrapper.querySelector('.elementor-swiper-button-next');
            if (prev && next) {
                [prev, next].forEach(el => {
                    el.style.transition = 'opacity 0.3s ease';
                    el.style.opacity = show ? '1' : '0';
                    el.style.pointerEvents = show ? 'auto' : 'none';
                });
            }
        }
    }, 100);
});

jQuery(document).ready(function($) {
    jQuery(document).on("click", ".open_text", function(e) {
        e.preventDefault();

        const $btn = jQuery(this);
        const $container = $btn.closest(".column_text");
        const $textBlock = $container.find(".wrap_text");

        $textBlock.toggle(300);

        jQuery(this).toggleClass('active_button');
    });
});

// Lightbox
document.addEventListener('DOMContentLoaded', function () {
    document.addEventListener('click', function () {
        var lightbox = document.querySelector('.elementor-lightbox');
        // Check if lightbox exists and is currently open/visible
        var isVisible = lightbox && lightbox.style.display !== 'none' && lightbox.style.visibility !== 'hidden' && lightbox.offsetHeight > 0;

        if (isVisible && document.fullscreenElement === null) {
            if (lightbox.requestFullscreen) {
                lightbox.requestFullscreen().catch(function () {
                    // silently fail if fullscreen is blocked
                });
            }
        }
    });
});
